#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML模板编辑器
支持拖拽调整元素位置，添加/删除元素，调整样式，导出HTML模板
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser
import json
import os
from typing import Dict, List, Tuple, Optional
import webbrowser
import tempfile

class TemplateElement:
    """模板元素类"""
    def __init__(self, element_type: str, x: int, y: int, width: int, height: int, 
                 text: str = "", style_class: str = "", element_id: str = ""):
        self.element_type = element_type  # text, table_header, table_cell, line, border
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.text = text
        self.style_class = style_class
        self.element_id = element_id or f"{element_type}_{id(self)}"
        
    def to_dict(self) -> Dict:
        return {
            'element_type': self.element_type,
            'x': self.x,
            'y': self.y,
            'width': self.width,
            'height': self.height,
            'text': self.text,
            'style_class': self.style_class,
            'element_id': self.element_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict):
        return cls(**data)

class StyleClass:
    """样式类"""
    def __init__(self, name: str, css_properties: Dict[str, str]):
        self.name = name
        self.css_properties = css_properties
    
    def to_css(self) -> str:
        """转换为CSS字符串"""
        properties = []
        for prop, value in self.css_properties.items():
            properties.append(f"\t{prop}: {value};")
        return f".{self.name}\n{{\n" + "\n".join(properties) + "\n}"
    
    def to_dict(self) -> Dict:
        return {
            'name': self.name,
            'css_properties': self.css_properties
        }
    
    @classmethod
    def from_dict(cls, data: Dict):
        return cls(data['name'], data['css_properties'])

class TemplateEditor:
    """模板编辑器主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("HTML模板编辑器")
        self.root.geometry("1200x800")
        
        # 模板数据
        self.elements: List[TemplateElement] = []
        self.styles: Dict[str, StyleClass] = {}
        self.template_width = 794
        self.template_height = 351
        self.current_file = None
        
        # 选中的元素
        self.selected_element: Optional[TemplateElement] = None
        self.drag_start_x = 0
        self.drag_start_y = 0
        
        # 初始化默认样式
        self._init_default_styles()
        
        # 创建界面
        self._create_ui()
        
        # 加载现有模板（如果存在）
        self._load_existing_template()
    
    def _init_default_styles(self):
        """初始化默认样式"""
        default_styles = {
            's11': {'background': 'white'},
            's12': {'color': 'black', 'font': '9pt 宋体', 'text-align': 'center', 
                   'vertical-align': 'middle', 'line-height': '14px'},
            's13': {'color': 'black', 'font': 'bold 20pt 宋体', 'text-align': 'center',
                   'vertical-align': 'middle', 'line-height': '29px', 'z-index': '1'},
            's14': {'border-bottom': '2px solid black'},
            's15': {'color': 'black', 'font': '9pt 宋体', 'text-align': 'right',
                   'vertical-align': 'middle', 'line-height': '14px', 'padding-right': '2px'},
            's16': {'border-top': '1px solid black'},
            's17': {'border-left': '1px solid black'},
            's18': {'color': 'black', 'font': '9pt 宋体', 'vertical-align': 'middle',
                   'line-height': '14px', 'padding-left': '2px'},
            's19': {'border-top': '2px solid black'},
            's20': {'color': 'black', 'font': 'bold 10pt 宋体', 'text-align': 'center',
                   'vertical-align': 'middle', 'line-height': '15px'},
            's21': {'color': 'black', 'font': '9pt 宋体', 'vertical-align': 'top',
                   'line-height': '14px', 'padding-top': '1px', 'padding-left': '2px'}
        }
        
        for name, props in default_styles.items():
            self.styles[name] = StyleClass(name, props)
    
    def _create_ui(self):
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建工具栏
        self._create_toolbar(main_frame)
        
        # 创建主要内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        # 左侧面板（工具和属性）
        left_panel = ttk.Frame(content_frame, width=250)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        left_panel.pack_propagate(False)
        
        # 右侧画布区域
        canvas_frame = ttk.Frame(content_frame)
        canvas_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 创建左侧面板内容
        self._create_left_panel(left_panel)
        
        # 创建画布
        self._create_canvas(canvas_frame)
    
    def _create_toolbar(self, parent):
        """创建工具栏"""
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, pady=(0, 5))
        
        # 文件操作按钮
        ttk.Button(toolbar, text="新建", command=self.new_template).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="打开", command=self.open_template).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="保存", command=self.save_template).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="另存为", command=self.save_as_template).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # 导出按钮
        ttk.Button(toolbar, text="导出HTML", command=self.export_html).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="预览", command=self.preview_html).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # 模板尺寸设置
        ttk.Label(toolbar, text="宽度:").pack(side=tk.LEFT, padx=(0, 2))
        self.width_var = tk.StringVar(value=str(self.template_width))
        width_entry = ttk.Entry(toolbar, textvariable=self.width_var, width=6)
        width_entry.pack(side=tk.LEFT, padx=(0, 5))
        width_entry.bind('<Return>', self._update_template_size)
        
        ttk.Label(toolbar, text="高度:").pack(side=tk.LEFT, padx=(0, 2))
        self.height_var = tk.StringVar(value=str(self.template_height))
        height_entry = ttk.Entry(toolbar, textvariable=self.height_var, width=6)
        height_entry.pack(side=tk.LEFT, padx=(0, 5))
        height_entry.bind('<Return>', self._update_template_size)

    def _create_left_panel(self, parent):
        """创建左侧面板"""
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True)

        # 元素工具页
        tools_frame = ttk.Frame(notebook)
        notebook.add(tools_frame, text="工具")
        self._create_tools_panel(tools_frame)

        # 属性编辑页
        properties_frame = ttk.Frame(notebook)
        notebook.add(properties_frame, text="属性")
        self._create_properties_panel(properties_frame)

        # 样式管理页
        styles_frame = ttk.Frame(notebook)
        notebook.add(styles_frame, text="样式")
        self._create_styles_panel(styles_frame)

    def _create_tools_panel(self, parent):
        """创建工具面板"""
        # 添加元素按钮
        ttk.Label(parent, text="添加元素:", font=('Arial', 10, 'bold')).pack(anchor=tk.W, pady=(5, 2))

        tools_frame = ttk.Frame(parent)
        tools_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(tools_frame, text="文本", command=lambda: self._add_element('text')).pack(fill=tk.X, pady=1)
        ttk.Button(tools_frame, text="表头", command=lambda: self._add_element('table_header')).pack(fill=tk.X, pady=1)
        ttk.Button(tools_frame, text="表格单元", command=lambda: self._add_element('table_cell')).pack(fill=tk.X, pady=1)
        ttk.Button(tools_frame, text="水平线", command=lambda: self._add_element('h_line')).pack(fill=tk.X, pady=1)
        ttk.Button(tools_frame, text="垂直线", command=lambda: self._add_element('v_line')).pack(fill=tk.X, pady=1)

        ttk.Separator(parent, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=10)

        # 操作按钮
        ttk.Label(parent, text="操作:", font=('Arial', 10, 'bold')).pack(anchor=tk.W, pady=(0, 2))

        ops_frame = ttk.Frame(parent)
        ops_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(ops_frame, text="删除选中", command=self._delete_selected).pack(fill=tk.X, pady=1)
        ttk.Button(ops_frame, text="复制", command=self._copy_selected).pack(fill=tk.X, pady=1)
        ttk.Button(ops_frame, text="粘贴", command=self._paste_element).pack(fill=tk.X, pady=1)

        ttk.Separator(parent, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=10)

        # 对齐工具
        ttk.Label(parent, text="对齐:", font=('Arial', 10, 'bold')).pack(anchor=tk.W, pady=(0, 2))

        align_frame = ttk.Frame(parent)
        align_frame.pack(fill=tk.X)

        align_row1 = ttk.Frame(align_frame)
        align_row1.pack(fill=tk.X, pady=1)
        ttk.Button(align_row1, text="左对齐", command=lambda: self._align_elements('left')).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 1))
        ttk.Button(align_row1, text="右对齐", command=lambda: self._align_elements('right')).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(1, 0))

        align_row2 = ttk.Frame(align_frame)
        align_row2.pack(fill=tk.X, pady=1)
        ttk.Button(align_row2, text="顶对齐", command=lambda: self._align_elements('top')).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 1))
        ttk.Button(align_row2, text="底对齐", command=lambda: self._align_elements('bottom')).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(1, 0))

    def _create_properties_panel(self, parent):
        """创建属性面板"""
        # 滚动框架
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        self.properties_frame = scrollable_frame

        # 初始化属性控件
        self._init_property_widgets()

    def _init_property_widgets(self):
        """初始化属性控件"""
        # 清空现有控件
        for widget in self.properties_frame.winfo_children():
            widget.destroy()

        ttk.Label(self.properties_frame, text="元素属性", font=('Arial', 12, 'bold')).pack(anchor=tk.W, pady=(5, 10))

        if not self.selected_element:
            ttk.Label(self.properties_frame, text="请选择一个元素").pack(anchor=tk.W, pady=5)
            return

        # 基本属性
        basic_frame = ttk.LabelFrame(self.properties_frame, text="基本属性")
        basic_frame.pack(fill=tk.X, pady=(0, 10), padx=5)

        # 位置和尺寸
        ttk.Label(basic_frame, text="X坐标:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.x_var = tk.StringVar(value=str(self.selected_element.x))
        x_entry = ttk.Entry(basic_frame, textvariable=self.x_var, width=10)
        x_entry.grid(row=0, column=1, padx=5, pady=2)
        x_entry.bind('<Return>', self._update_element_properties)

        ttk.Label(basic_frame, text="Y坐标:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.y_var = tk.StringVar(value=str(self.selected_element.y))
        y_entry = ttk.Entry(basic_frame, textvariable=self.y_var, width=10)
        y_entry.grid(row=1, column=1, padx=5, pady=2)
        y_entry.bind('<Return>', self._update_element_properties)

        ttk.Label(basic_frame, text="宽度:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.width_elem_var = tk.StringVar(value=str(self.selected_element.width))
        width_entry = ttk.Entry(basic_frame, textvariable=self.width_elem_var, width=10)
        width_entry.grid(row=2, column=1, padx=5, pady=2)
        width_entry.bind('<Return>', self._update_element_properties)

        ttk.Label(basic_frame, text="高度:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        self.height_elem_var = tk.StringVar(value=str(self.selected_element.height))
        height_entry = ttk.Entry(basic_frame, textvariable=self.height_elem_var, width=10)
        height_entry.grid(row=3, column=1, padx=5, pady=2)
        height_entry.bind('<Return>', self._update_element_properties)

        # 文本内容（如果是文本元素）
        if self.selected_element.element_type in ['text', 'table_header', 'table_cell']:
            ttk.Label(basic_frame, text="文本:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=2)
            self.text_var = tk.StringVar(value=self.selected_element.text)
            text_entry = ttk.Entry(basic_frame, textvariable=self.text_var, width=15)
            text_entry.grid(row=4, column=1, padx=5, pady=2)
            text_entry.bind('<Return>', self._update_element_properties)

        # 样式类选择
        style_frame = ttk.LabelFrame(self.properties_frame, text="样式")
        style_frame.pack(fill=tk.X, pady=(0, 10), padx=5)

        ttk.Label(style_frame, text="样式类:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.style_var = tk.StringVar(value=self.selected_element.style_class)
        style_combo = ttk.Combobox(style_frame, textvariable=self.style_var, values=list(self.styles.keys()), width=12)
        style_combo.grid(row=0, column=1, padx=5, pady=2)
        style_combo.bind('<<ComboboxSelected>>', self._update_element_properties)

    def _create_styles_panel(self, parent):
        """创建样式面板"""
        # 样式列表
        ttk.Label(parent, text="样式列表:", font=('Arial', 10, 'bold')).pack(anchor=tk.W, pady=(5, 2))

        list_frame = ttk.Frame(parent)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        self.styles_listbox = tk.Listbox(list_frame, height=8)
        styles_scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.styles_listbox.yview)
        self.styles_listbox.configure(yscrollcommand=styles_scrollbar.set)

        self.styles_listbox.pack(side="left", fill="both", expand=True)
        styles_scrollbar.pack(side="right", fill="y")

        self.styles_listbox.bind('<<ListboxSelect>>', self._on_style_select)

        # 样式操作按钮
        style_ops_frame = ttk.Frame(parent)
        style_ops_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(style_ops_frame, text="新建样式", command=self._create_new_style).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(style_ops_frame, text="删除样式", command=self._delete_style).pack(side=tk.LEFT)

        # 更新样式列表
        self._update_styles_list()

    def _create_canvas(self, parent):
        """创建画布"""
        # 创建画布框架
        canvas_container = ttk.Frame(parent)
        canvas_container.pack(fill=tk.BOTH, expand=True)

        # 创建滚动条
        h_scrollbar = ttk.Scrollbar(canvas_container, orient=tk.HORIZONTAL)
        v_scrollbar = ttk.Scrollbar(canvas_container, orient=tk.VERTICAL)

        # 创建画布
        self.canvas = tk.Canvas(canvas_container,
                               bg='white',
                               scrollregion=(0, 0, self.template_width + 100, self.template_height + 100),
                               xscrollcommand=h_scrollbar.set,
                               yscrollcommand=v_scrollbar.set)

        # 配置滚动条
        h_scrollbar.config(command=self.canvas.xview)
        v_scrollbar.config(command=self.canvas.yview)

        # 布局
        self.canvas.grid(row=0, column=0, sticky="nsew")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")

        canvas_container.grid_rowconfigure(0, weight=1)
        canvas_container.grid_columnconfigure(0, weight=1)

        # 绑定事件
        self.canvas.bind("<Button-1>", self._on_canvas_click)
        self.canvas.bind("<B1-Motion>", self._on_canvas_drag)
        self.canvas.bind("<ButtonRelease-1>", self._on_canvas_release)
        self.canvas.bind("<Double-Button-1>", self._on_canvas_double_click)

        # 绘制模板边框
        self._draw_template_border()

        # 绘制网格
        self._draw_grid()

    def _draw_template_border(self):
        """绘制模板边框"""
        self.canvas.create_rectangle(10, 10,
                                   self.template_width + 10,
                                   self.template_height + 10,
                                   outline='red', width=2, tags='template_border')

    def _draw_grid(self):
        """绘制网格"""
        # 删除现有网格
        self.canvas.delete('grid')

        # 绘制垂直网格线
        for x in range(0, self.template_width + 100, 20):
            self.canvas.create_line(x + 10, 10, x + 10, self.template_height + 10,
                                  fill='lightgray', tags='grid')

        # 绘制水平网格线
        for y in range(0, self.template_height + 100, 20):
            self.canvas.create_line(10, y + 10, self.template_width + 10, y + 10,
                                  fill='lightgray', tags='grid')

    def _load_existing_template(self):
        """加载现有模板"""
        if os.path.exists('打印模板.html'):
            try:
                # 这里可以添加HTML解析逻辑
                # 暂时添加一些示例元素
                self._add_sample_elements()
            except Exception as e:
                print(f"加载模板失败: {e}")

    def _add_sample_elements(self):
        """添加示例元素"""
        # 添加标题
        title = TemplateElement('text', 39, 16, 718, 35, '廊坊新颖不干胶印刷业务单', 's13')
        self.elements.append(title)

        # 添加一些表头
        headers = [
            ('序号', 39, 102, 29, 15),
            ('项目类型', 72, 102, 85, 15),
            ('内容', 161, 102, 104, 15),
            ('规格', 348, 102, 71, 15),
            ('单位', 420, 102, 42, 15),
            ('数量', 464, 102, 37, 15),
            ('单价', 562, 102, 30, 15),
            ('金额', 604, 102, 42, 15)
        ]

        for text, x, y, w, h in headers:
            header = TemplateElement('table_header', x, y, w, h, text, 's12')
            self.elements.append(header)

        self._redraw_canvas()

    def _redraw_canvas(self):
        """重绘画布"""
        # 清除所有元素
        self.canvas.delete('element')

        # 绘制所有元素
        for element in self.elements:
            self._draw_element(element)

    def _draw_element(self, element: TemplateElement):
        """绘制单个元素"""
        x1 = element.x + 10
        y1 = element.y + 10
        x2 = x1 + element.width
        y2 = y1 + element.height

        # 根据元素类型绘制
        if element.element_type in ['text', 'table_header', 'table_cell']:
            # 绘制背景矩形
            rect_id = self.canvas.create_rectangle(x1, y1, x2, y2,
                                                 outline='black', fill='white',
                                                 tags=('element', element.element_id))

            # 绘制文本
            text_id = self.canvas.create_text(x1 + element.width/2, y1 + element.height/2,
                                            text=element.text, anchor='center',
                                            tags=('element', element.element_id))

        elif element.element_type == 'h_line':
            # 水平线
            line_id = self.canvas.create_line(x1, y1, x2, y1,
                                            fill='black', width=1,
                                            tags=('element', element.element_id))

        elif element.element_type == 'v_line':
            # 垂直线
            line_id = self.canvas.create_line(x1, y1, x1, y2,
                                            fill='black', width=1,
                                            tags=('element', element.element_id))

        # 如果是选中的元素，添加选择框
        if element == self.selected_element:
            self._draw_selection_box(element)

    def _draw_selection_box(self, element: TemplateElement):
        """绘制选择框"""
        x1 = element.x + 10 - 2
        y1 = element.y + 10 - 2
        x2 = x1 + element.width + 4
        y2 = y1 + element.height + 4

        self.canvas.create_rectangle(x1, y1, x2, y2,
                                   outline='blue', width=2, dash=(5, 5),
                                   tags='selection')

    def _on_canvas_click(self, event):
        """画布点击事件"""
        # 转换坐标
        x = self.canvas.canvasx(event.x) - 10
        y = self.canvas.canvasy(event.y) - 10

        # 查找点击的元素
        clicked_element = None
        for element in reversed(self.elements):  # 从上层开始查找
            if (element.x <= x <= element.x + element.width and
                element.y <= y <= element.y + element.height):
                clicked_element = element
                break

        # 更新选中元素
        self.selected_element = clicked_element
        self.drag_start_x = x
        self.drag_start_y = y

        # 更新属性面板
        self._init_property_widgets()

        # 重绘画布
        self.canvas.delete('selection')
        self._redraw_canvas()

    def _on_canvas_drag(self, event):
        """画布拖拽事件"""
        if not self.selected_element:
            return

        # 转换坐标
        x = self.canvas.canvasx(event.x) - 10
        y = self.canvas.canvasy(event.y) - 10

        # 计算偏移
        dx = x - self.drag_start_x
        dy = y - self.drag_start_y

        # 更新元素位置
        self.selected_element.x += dx
        self.selected_element.y += dy

        # 限制在模板范围内
        self.selected_element.x = max(0, min(self.selected_element.x,
                                           self.template_width - self.selected_element.width))
        self.selected_element.y = max(0, min(self.selected_element.y,
                                           self.template_height - self.selected_element.height))

        # 更新拖拽起始点
        self.drag_start_x = self.selected_element.x
        self.drag_start_y = self.selected_element.y

        # 重绘画布
        self.canvas.delete('selection')
        self._redraw_canvas()

        # 更新属性面板
        if hasattr(self, 'x_var'):
            self.x_var.set(str(self.selected_element.x))
            self.y_var.set(str(self.selected_element.y))

    def _on_canvas_release(self, event):
        """画布释放事件"""
        pass

    def _on_canvas_double_click(self, event):
        """画布双击事件"""
        if self.selected_element and self.selected_element.element_type in ['text', 'table_header', 'table_cell']:
            # 打开文本编辑对话框
            self._edit_text_dialog()

    def _edit_text_dialog(self):
        """文本编辑对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("编辑文本")
        dialog.geometry("300x150")
        dialog.transient(self.root)
        dialog.grab_set()

        ttk.Label(dialog, text="文本内容:").pack(pady=10)

        text_var = tk.StringVar(value=self.selected_element.text)
        entry = ttk.Entry(dialog, textvariable=text_var, width=30)
        entry.pack(pady=5)
        entry.focus()

        def save_text():
            self.selected_element.text = text_var.get()
            self._redraw_canvas()
            self._init_property_widgets()
            dialog.destroy()

        def cancel():
            dialog.destroy()

        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)

        ttk.Button(button_frame, text="确定", command=save_text).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=cancel).pack(side=tk.LEFT, padx=5)

        entry.bind('<Return>', lambda e: save_text())
        dialog.bind('<Escape>', lambda e: cancel())

    def _add_element(self, element_type: str):
        """添加元素"""
        # 在画布中心添加新元素
        x = self.template_width // 2 - 50
        y = self.template_height // 2 - 15

        if element_type == 'text':
            element = TemplateElement(element_type, x, y, 100, 30, "新文本", 's18')
        elif element_type == 'table_header':
            element = TemplateElement(element_type, x, y, 80, 15, "表头", 's12')
        elif element_type == 'table_cell':
            element = TemplateElement(element_type, x, y, 80, 15, "单元格", 's18')
        elif element_type == 'h_line':
            element = TemplateElement(element_type, x, y, 100, 1, "", 's16')
        elif element_type == 'v_line':
            element = TemplateElement(element_type, x, y, 1, 50, "", 's17')
        else:
            return

        self.elements.append(element)
        self.selected_element = element

        self._redraw_canvas()
        self._init_property_widgets()

    def _delete_selected(self):
        """删除选中元素"""
        if self.selected_element and self.selected_element in self.elements:
            self.elements.remove(self.selected_element)
            self.selected_element = None
            self._redraw_canvas()
            self._init_property_widgets()

    def _copy_selected(self):
        """复制选中元素"""
        if self.selected_element:
            self.copied_element = TemplateElement(
                self.selected_element.element_type,
                self.selected_element.x + 20,
                self.selected_element.y + 20,
                self.selected_element.width,
                self.selected_element.height,
                self.selected_element.text,
                self.selected_element.style_class
            )

    def _paste_element(self):
        """粘贴元素"""
        if hasattr(self, 'copied_element'):
            new_element = TemplateElement(
                self.copied_element.element_type,
                self.copied_element.x,
                self.copied_element.y,
                self.copied_element.width,
                self.copied_element.height,
                self.copied_element.text,
                self.copied_element.style_class
            )
            self.elements.append(new_element)
            self.selected_element = new_element
            self._redraw_canvas()
            self._init_property_widgets()

    def _align_elements(self, direction: str):
        """对齐元素"""
        if not self.selected_element:
            return

        # 这里可以实现多选元素的对齐功能
        # 暂时只处理单个元素的网格对齐
        grid_size = 10

        if direction == 'left':
            self.selected_element.x = (self.selected_element.x // grid_size) * grid_size
        elif direction == 'right':
            self.selected_element.x = ((self.selected_element.x + self.selected_element.width) // grid_size) * grid_size - self.selected_element.width
        elif direction == 'top':
            self.selected_element.y = (self.selected_element.y // grid_size) * grid_size
        elif direction == 'bottom':
            self.selected_element.y = ((self.selected_element.y + self.selected_element.height) // grid_size) * grid_size - self.selected_element.height

        self._redraw_canvas()
        self._init_property_widgets()

    def _update_element_properties(self, event=None):
        """更新元素属性"""
        if not self.selected_element:
            return

        try:
            # 更新位置和尺寸
            if hasattr(self, 'x_var'):
                self.selected_element.x = int(self.x_var.get())
            if hasattr(self, 'y_var'):
                self.selected_element.y = int(self.y_var.get())
            if hasattr(self, 'width_elem_var'):
                self.selected_element.width = int(self.width_elem_var.get())
            if hasattr(self, 'height_elem_var'):
                self.selected_element.height = int(self.height_elem_var.get())

            # 更新文本
            if hasattr(self, 'text_var'):
                self.selected_element.text = self.text_var.get()

            # 更新样式类
            if hasattr(self, 'style_var'):
                self.selected_element.style_class = self.style_var.get()

            self._redraw_canvas()
        except ValueError:
            pass  # 忽略无效输入

    def _update_template_size(self, event=None):
        """更新模板尺寸"""
        try:
            self.template_width = int(self.width_var.get())
            self.template_height = int(self.height_var.get())

            # 更新画布滚动区域
            self.canvas.configure(scrollregion=(0, 0, self.template_width + 100, self.template_height + 100))

            # 重绘边框和网格
            self.canvas.delete('template_border')
            self.canvas.delete('grid')
            self._draw_template_border()
            self._draw_grid()
        except ValueError:
            pass

    def _update_styles_list(self):
        """更新样式列表"""
        self.styles_listbox.delete(0, tk.END)
        for style_name in self.styles.keys():
            self.styles_listbox.insert(tk.END, style_name)

    def _on_style_select(self, event):
        """样式选择事件"""
        selection = self.styles_listbox.curselection()
        if selection:
            style_name = self.styles_listbox.get(selection[0])
            # 这里可以显示样式详情

    def _create_new_style(self):
        """创建新样式"""
        dialog = tk.Toplevel(self.root)
        dialog.title("新建样式")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        ttk.Label(dialog, text="样式名称:").pack(pady=5)
        name_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=name_var, width=30).pack(pady=5)

        ttk.Label(dialog, text="CSS属性 (格式: 属性:值, 每行一个):").pack(pady=(10, 5))

        text_widget = tk.Text(dialog, width=50, height=10)
        text_widget.pack(pady=5, padx=10, fill=tk.BOTH, expand=True)

        def save_style():
            name = name_var.get().strip()
            if not name:
                messagebox.showerror("错误", "请输入样式名称")
                return

            css_text = text_widget.get("1.0", tk.END).strip()
            css_properties = {}

            for line in css_text.split('\n'):
                if ':' in line:
                    prop, value = line.split(':', 1)
                    css_properties[prop.strip()] = value.strip()

            self.styles[name] = StyleClass(name, css_properties)
            self._update_styles_list()
            dialog.destroy()

        ttk.Button(dialog, text="保存", command=save_style).pack(pady=10)

    def _delete_style(self):
        """删除样式"""
        selection = self.styles_listbox.curselection()
        if selection:
            style_name = self.styles_listbox.get(selection[0])
            if style_name in self.styles:
                del self.styles[style_name]
                self._update_styles_list()

    def new_template(self):
        """新建模板"""
        self.elements.clear()
        self.selected_element = None
        self.current_file = None
        self._redraw_canvas()
        self._init_property_widgets()

    def open_template(self):
        """打开模板"""
        filename = filedialog.askopenfilename(
            title="打开模板",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 加载元素
                self.elements.clear()
                for elem_data in data.get('elements', []):
                    element = TemplateElement.from_dict(elem_data)
                    self.elements.append(element)

                # 加载样式
                self.styles.clear()
                for style_data in data.get('styles', []):
                    style = StyleClass.from_dict(style_data)
                    self.styles[style.name] = style

                # 加载模板尺寸
                self.template_width = data.get('template_width', 794)
                self.template_height = data.get('template_height', 351)

                self.width_var.set(str(self.template_width))
                self.height_var.set(str(self.template_height))

                self.current_file = filename
                self.selected_element = None

                self._update_template_size()
                self._update_styles_list()
                self._redraw_canvas()
                self._init_property_widgets()

            except Exception as e:
                messagebox.showerror("错误", f"打开文件失败: {e}")

    def save_template(self):
        """保存模板"""
        if self.current_file:
            self._save_to_file(self.current_file)
        else:
            self.save_as_template()

    def save_as_template(self):
        """另存为模板"""
        filename = filedialog.asksaveasfilename(
            title="保存模板",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if filename:
            self._save_to_file(filename)
            self.current_file = filename

    def _save_to_file(self, filename: str):
        """保存到文件"""
        try:
            data = {
                'template_width': self.template_width,
                'template_height': self.template_height,
                'elements': [elem.to_dict() for elem in self.elements],
                'styles': [style.to_dict() for style in self.styles.values()]
            }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("成功", "模板保存成功")
        except Exception as e:
            messagebox.showerror("错误", f"保存文件失败: {e}")

    def export_html(self):
        """导出HTML"""
        filename = filedialog.asksaveasfilename(
            title="导出HTML",
            defaultextension=".html",
            filetypes=[("HTML文件", "*.html"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                html_content = self._generate_html()
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                messagebox.showinfo("成功", "HTML导出成功")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {e}")

    def preview_html(self):
        """预览HTML"""
        try:
            html_content = self._generate_html()

            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_filename = f.name

            # 在浏览器中打开
            webbrowser.open('file://' + os.path.abspath(temp_filename))
        except Exception as e:
            messagebox.showerror("错误", f"预览失败: {e}")

    def _generate_html(self) -> str:
        """生成HTML内容"""
        html_parts = []

        # HTML头部
        html_parts.append('<!doctype html>')
        html_parts.append('<html>')
        html_parts.append('<head>')
        html_parts.append('<title>模板预览</title>')
        html_parts.append('<meta http-equiv="Content-Type" content="text/html; charset=utf-8">')
        html_parts.append('</head>')
        html_parts.append('<body>')

        # 页面容器
        html_parts.append(f'<div id="page1" class="page" style="position:relative;width:{self.template_width}px;height:{self.template_height}px">')

        # 生成元素
        for element in self.elements:
            html_parts.append(self._element_to_html(element))

        html_parts.append('</div>')

        # CSS样式
        html_parts.append('<style>')
        html_parts.append(self._generate_css())
        html_parts.append('</style>')

        html_parts.append('</body>')
        html_parts.append('</html>')

        return '\n'.join(html_parts)

    def _element_to_html(self, element: TemplateElement) -> str:
        """将元素转换为HTML"""
        if element.element_type in ['text', 'table_header', 'table_cell']:
            # 背景div
            bg_div = f'<div class="s11" style="left:{element.x}px;top:{element.y}px;width:{element.width}px;height:{element.height}px;"></div>'

            # 文本div
            text_div = f'<div class="{element.style_class}" style="left:{element.x}px;top:{element.y}px;width:{element.width}px;height:{element.height}px;">'
            text_div += '<table><tr><td>' + element.text + '</table></div>'

            return bg_div + '\n' + text_div

        elif element.element_type == 'h_line':
            return f'<div class="{element.style_class}" style="left:{element.x}px;top:{element.y}px;width:{element.width}px;height:0;"></div>'

        elif element.element_type == 'v_line':
            return f'<div class="{element.style_class}" style="left:{element.x}px;top:{element.y}px;width:0;height:{element.height}px;"></div>'

        return ''

    def _generate_css(self) -> str:
        """生成CSS样式"""
        css_parts = []

        # 基础样式
        css_parts.append('''div, img, table {
    position: absolute;
}
div, td {
    overflow: hidden;
}
table {
    width: 100%;
    height: 100%;
    border-spacing: 0;
}
.page {
    border: 1px solid black;
    margin: 0 auto;
}''')

        # 自定义样式
        for style in self.styles.values():
            css_parts.append(style.to_css())

        return '\n'.join(css_parts)

    def run(self):
        """运行编辑器"""
        self.root.mainloop()

def main():
    """主函数"""
    editor = TemplateEditor()
    editor.run()

if __name__ == "__main__":
    main()
