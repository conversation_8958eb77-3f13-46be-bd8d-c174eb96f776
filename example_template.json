{"template_width": 794, "template_height": 351, "elements": [{"element_type": "text", "x": 39, "y": 16, "width": 718, "height": 35, "text": "廊坊新颖不干胶印刷业务单", "style_class": "s13", "element_id": "title"}, {"element_type": "text", "x": 37, "y": 54, "width": 62, "height": 19, "text": "订单编号：", "style_class": "s15", "element_id": "order_label"}, {"element_type": "text", "x": 202, "y": 54, "width": 63, "height": 19, "text": "客户名称：", "style_class": "s15", "element_id": "customer_label"}, {"element_type": "table_header", "x": 39, "y": 102, "width": 29, "height": 15, "text": "序号", "style_class": "s12", "element_id": "header_no"}, {"element_type": "table_header", "x": 72, "y": 102, "width": 85, "height": 15, "text": "项目类型", "style_class": "s12", "element_id": "header_type"}, {"element_type": "table_header", "x": 161, "y": 102, "width": 104, "height": 15, "text": "内容", "style_class": "s12", "element_id": "header_content"}, {"element_type": "table_header", "x": 348, "y": 102, "width": 71, "height": 15, "text": "规格", "style_class": "s12", "element_id": "header_spec"}, {"element_type": "table_header", "x": 420, "y": 102, "width": 42, "height": 15, "text": "单位", "style_class": "s12", "element_id": "header_unit"}, {"element_type": "table_header", "x": 464, "y": 102, "width": 37, "height": 15, "text": "数量", "style_class": "s12", "element_id": "header_qty"}, {"element_type": "table_header", "x": 562, "y": 102, "width": 30, "height": 15, "text": "单价", "style_class": "s12", "element_id": "header_price"}, {"element_type": "table_header", "x": 604, "y": 102, "width": 42, "height": 15, "text": "金额", "style_class": "s12", "element_id": "header_amount"}, {"element_type": "h_line", "x": 39, "y": 97, "width": 719, "height": 0, "text": "", "style_class": "s16", "element_id": "top_line"}, {"element_type": "h_line", "x": 39, "y": 121, "width": 719, "height": 0, "text": "", "style_class": "s16", "element_id": "header_line"}, {"element_type": "v_line", "x": 39, "y": 98, "width": 0, "height": 24, "text": "", "style_class": "s17", "element_id": "left_line"}, {"element_type": "v_line", "x": 69, "y": 98, "width": 0, "height": 24, "text": "", "style_class": "s17", "element_id": "col1_line"}, {"element_type": "v_line", "x": 158, "y": 98, "width": 0, "height": 24, "text": "", "style_class": "s17", "element_id": "col2_line"}], "styles": [{"name": "s11", "css_properties": {"background": "white"}}, {"name": "s12", "css_properties": {"color": "black", "font": "9pt 宋体", "text-align": "center", "vertical-align": "middle", "line-height": "14px"}}, {"name": "s13", "css_properties": {"color": "black", "font": "bold 20pt 宋体", "text-align": "center", "vertical-align": "middle", "line-height": "29px", "z-index": "1"}}, {"name": "s15", "css_properties": {"color": "black", "font": "9pt 宋体", "text-align": "right", "vertical-align": "middle", "line-height": "14px", "padding-right": "2px"}}, {"name": "s16", "css_properties": {"border-top": "1px solid black"}}, {"name": "s17", "css_properties": {"border-left": "1px solid black"}}, {"name": "s18", "css_properties": {"color": "black", "font": "9pt 宋体", "vertical-align": "middle", "line-height": "14px", "padding-left": "2px"}}]}