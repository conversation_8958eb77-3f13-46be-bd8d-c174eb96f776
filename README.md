# HTML模板编辑器

一个用Python和Tkinter开发的可视化HTML模板编辑器，专门用于创建和编辑打印模板。

## 功能特性

### 🎨 可视化编辑
- 拖拽调整元素位置
- 实时预览效果
- 网格对齐功能
- 选择框显示

### 📝 元素管理
- **文本元素**: 添加和编辑文本内容
- **表格元素**: 表头和单元格
- **线条元素**: 水平线和垂直线
- **复制粘贴**: 快速复制元素

### 🎯 精确控制
- 像素级位置调整
- 尺寸精确设置
- 属性面板编辑
- 样式类管理

### 💾 文件操作
- 保存/加载模板项目(.json)
- 导出HTML文件
- 浏览器预览功能

### 🎨 样式管理
- 自定义CSS样式类
- 预设样式模板
- 样式实时应用

## 安装要求

- Python 3.6+
- tkinter (通常随Python安装)
- 标准库模块: json, os, tempfile, webbrowser

## 使用方法

### 启动编辑器
```bash
python run_editor.py
```
或者
```bash
python template_editor.py
```

### 基本操作

#### 1. 添加元素
- 在左侧"工具"面板中点击相应按钮
- 元素会在画布中心创建
- 可以立即拖拽到目标位置

#### 2. 编辑元素
- **单击选择**: 点击元素进行选择
- **拖拽移动**: 选中后直接拖拽
- **双击编辑**: 双击文本元素快速编辑内容
- **属性面板**: 在"属性"标签页精确调整

#### 3. 样式设置
- 在"样式"标签页管理CSS样式类
- 为元素分配样式类
- 创建自定义样式

#### 4. 文件操作
- **新建**: 创建空白模板
- **打开**: 加载已保存的模板项目
- **保存**: 保存当前模板为.json文件
- **导出HTML**: 生成最终的HTML文件
- **预览**: 在浏览器中预览效果

### 快捷键
- **Delete**: 删除选中元素
- **Ctrl+C**: 复制选中元素
- **Ctrl+V**: 粘贴元素
- **双击**: 编辑文本内容

### 对齐工具
- 左对齐、右对齐
- 顶对齐、底对齐
- 网格对齐功能

## 文件格式

### 项目文件(.json)
编辑器使用JSON格式保存项目，包含：
- 模板尺寸设置
- 所有元素的位置、尺寸、内容
- 自定义样式定义

### 导出HTML
生成标准HTML文件，包含：
- 完整的HTML结构
- 内联CSS样式
- 绝对定位布局

## 示例用法

1. **创建业务单模板**
   - 添加标题文本
   - 创建表格结构
   - 添加分隔线
   - 设置样式和对齐

2. **编辑现有模板**
   - 打开HTML模板文件
   - 调整元素位置
   - 修改文本内容
   - 导出新版本

3. **批量模板制作**
   - 创建基础模板
   - 保存为项目文件
   - 复制并修改创建变体

## 技术特点

- **纯Python实现**: 无需额外依赖
- **跨平台支持**: Windows、macOS、Linux
- **轻量级**: 启动快速，占用资源少
- **可扩展**: 易于添加新功能

## 注意事项

1. **坐标系统**: 使用像素为单位，左上角为(0,0)
2. **样式继承**: 元素样式基于CSS类定义
3. **文件编码**: 统一使用UTF-8编码
4. **浏览器兼容**: 生成的HTML兼容现代浏览器

## 故障排除

### 常见问题
1. **无法启动**: 检查Python版本和tkinter安装
2. **中文显示问题**: 确保系统支持UTF-8编码
3. **预览失败**: 检查默认浏览器设置

### 性能优化
- 大量元素时可能影响拖拽性能
- 建议单个模板元素数量控制在100个以内

## 扩展开发

编辑器采用模块化设计，易于扩展：
- 添加新的元素类型
- 扩展样式编辑功能
- 集成更多导出格式
- 添加模板库功能

## 许可证

本项目采用MIT许可证，可自由使用和修改。
